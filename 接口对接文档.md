# 接口对接文档

以下是为vue3组合式api+ts前端开发准备的完整API接口文档，包括请求体和响应格式。
已经搭建好前端vue3+element plus+vant4项目环境，系统管理模块使用element plus，其他模块均使用vant4开发移动应用。
如果需要icon图标，请使用https://www.iconfont.cn/中寻找图标，并使用图标的svg代码
后端根路径为http://127.0.0.1:8000/
## 1. 用户管理接口

### 1.1 用户注册
**请求URL**: `POST /api/users/`  
**请求体**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string"
}
```
**响应格式**:
```json
{
  "id": 0,
  "username": "string",
  "email": "string",
  "phone": "string|null",
  "avatar": "string|null",
  "real_name": "string|null",
  "id_card": "string|null",
  "is_verified": false,
  "status": "active|inactive|banned",
  "roles": [],
  "date_joined": "datetime"
}
```

### 1.2 用户登录
**请求URL**: `POST /api/auth/login/`  
**请求体**:
```json
{
  "username": "string",
  "password": "string"
}
```
**响应格式**:
```json
{
  "token": "string",
  "user_id": 0,
  "username": "string",
  "email": "string"
}
```

### 1.3 用户登出
**请求URL**: `POST /api/auth/logout/`  
**请求头**: `Authorization: Token <token>`  
**响应格式**:
```json
{
  "message": "成功登出"
}
```

### 1.4 获取用户信息
**请求URL**: `GET /api/users/profile/`  
**请求头**: `Authorization: Token <token>`  
**响应格式**:
```json
{
  "id": 0,
  "username": "string",
  "email": "string",
  "phone": "string|null",
  "avatar": "string|null",
  "real_name": "string|null",
  "id_card": "string|null",
  "is_verified": false,
  "status": "active|inactive|banned",
  "roles": [],
  "date_joined": "datetime"
}
```

### 1.5 更新用户信息
**请求URL**: `PUT /api/users/update_profile/`  
**请求头**: `Authorization: Token <token>`  
**请求体**:
```json
{
  "phone": "string|null",
  "avatar": "string|null",
  "real_name": "string|null"
}
```
**响应格式**:
```json
{
  "id": 0,
  "username": "string",
  "email": "string",
  "phone": "string|null",
  "avatar": "string|null",
  "real_name": "string|null",
  "id_card": "string|null",
  "is_verified": false,
  "status": "active|inactive|banned",
  "roles": [],
  "date_joined": "datetime"
}
```

## 2. 服务分类接口

### 2.1 获取服务大类列表
**请求URL**: `GET /api/service-categories/`  
**响应格式**:
```json
[
  {
    "id": 0,
    "name": "string",
    "description": "string|null",
    "icon": "string|null",
    "sort_order": 0,
    "is_active": true,
    "created_at": "datetime",
    "updated_at": "datetime"
  }
]
```

### 2.2 获取服务子类列表
**请求URL**: `GET /api/service-subcategories/`  
**查询参数**: `category=<category_id>` (可选)  
**响应格式**:
```json
[
  {
    "id": 0,
    "category_name": "string",
    "name": "string",
    "description": "string|null",
    "icon": "string|null",
    "sort_order": 0,
    "is_active": true,
    "created_at": "datetime",
    "updated_at": "datetime",
    "category": 0
  }
]
```

## 3. 服务接口

### 3.1 发布服务
**请求URL**: `POST /api/services/`  
**请求头**: `Authorization: Token <token>`  
**请求体**:
```json
{
  "subcategory": 0,
  "title": "string",
  "description": "string|null",
  "price_type": "fixed|range",
  "price": "number|null", // price_type为fixed时必需
  "price_min": "number|null", // price_type为range时必需
  "price_max": "number|null", // price_type为range时必需
  "unit": "string",
  "location": "string|null",
  "service_area": "string|null"
}
```
**响应格式**:
```json
{
  "id": 0,
  "user_name": "string",
  "subcategory_name": "string",
  "title": "string",
  "description": "string|null",
  "price_type": "fixed|range",
  "price": "string|null",
  "price_min": "string|null",
  "price_max": "string|null",
  "unit": "string",
  "location": "string|null",
  "service_area": "string|null",
  "images": [],
  "status": "active|inactive|deleted",
  "created_at": "datetime",
  "updated_at": "datetime",
  "user": 0,
  "subcategory": 0
}
```

### 3.2 获取服务列表
**请求URL**: `GET /api/services/`  
**查询参数**: 
- `subcategory__category=<category_id>` (按大类筛选)
- `subcategory=<subcategory_id>` (按子类筛选)
- `user=<user_id>` (按用户筛选)
- `search=<keyword>` (按标题或描述搜索)
**响应格式**:
```json
[
  {
    "id": 0,
    "user_name": "string",
    "subcategory_name": "string",
    "title": "string",
    "description": "string|null",
    "price_type": "fixed|range",
    "price": "string|null",
    "price_min": "string|null",
    "price_max": "string|null",
    "unit": "string",
    "location": "string|null",
    "service_area": "string|null",
    "images": [],
    "status": "active|inactive|deleted",
    "created_at": "datetime",
    "updated_at": "datetime",
    "user": 0,
    "subcategory": 0
  }
]
```

### 3.3 获取我的服务列表
**请求URL**: `GET /api/services/my_services/`  
**请求头**: `Authorization: Token <token>`  
**响应格式**:
```json
[
  {
    "id": 0,
    "user_name": "string",
    "subcategory_name": "string",
    "title": "string",
    "description": "string|null",
    "price_type": "fixed|range",
    "price": "string|null",
    "price_min": "string|null",
    "price_max": "string|null",
    "unit": "string",
    "location": "string|null",
    "service_area": "string|null",
    "images": [],
    "status": "active|inactive|deleted",
    "created_at": "datetime",
    "updated_at": "datetime",
    "user": 0,
    "subcategory": 0
  }
]
```

## 4. 需求接口

### 4.1 发布需求
**请求URL**: `POST /api/demands/`  
**请求头**: `Authorization: Token <token>`  
**请求体**:
```json
{
  "subcategory": 0,
  "title": "string",
  "description": "string|null",
  "price_type": "fixed|range",
  "budget_min": "number|null", // price_type为fixed时必需
  "budget_max": "number|null", // price_type为range时必需
  "location": "string|null",
  "service_time": "datetime|null"
}
```
**响应格式**:
```json
{
  "id": 0,
  "user_name": "string",
  "subcategory_name": "string",
  "title": "string",
  "description": "string|null",
  "price_type": "fixed|range",
  "budget_min": "string|null",
  "budget_max": "string|null",
  "location": "string|null",
  "service_time": "datetime|null",
  "images": [],
  "status": "active|completed|cancelled|deleted",
  "created_at": "datetime",
  "updated_at": "datetime",
  "user": 0,
  "subcategory": 0,
  "category": 0|null
}
```

### 4.2 获取需求列表
**请求URL**: `GET /api/demands/`  
**查询参数**: 
- `category=<category_id>` (按大类筛选)
- `subcategory=<subcategory_id>` (按子类筛选)
- `user=<user_id>` (按用户筛选)
- `search=<keyword>` (按标题或描述搜索)
**响应格式**:
```json
[
  {
    "id": 0,
    "user_name": "string",
    "subcategory_name": "string",
    "title": "string",
    "description": "string|null",
    "price_type": "fixed|range",
    "budget_min": "string|null",
    "budget_max": "string|null",
    "location": "string|null",
    "service_time": "datetime|null",
    "images": [],
    "status": "active|completed|cancelled|deleted",
    "created_at": "datetime",
    "updated_at": "datetime",
    "user": 0,
    "subcategory": 0,
    "category": 0|null
  }
]
```

### 4.3 获取我的需求列表
**请求URL**: `GET /api/demands/my_demands/`  
**请求头**: `Authorization: Token <token>`  
**响应格式**:
```json
[
  {
    "id": 0,
    "user_name": "string",
    "subcategory_name": "string",
    "title": "string",
    "description": "string|null",
    "price_type": "fixed|range",
    "budget_min": "string|null",
    "budget_max": "string|null",
    "location": "string|null",
    "service_time": "datetime|null",
    "images": [],
    "status": "active|completed|cancelled|deleted",
    "created_at": "datetime",
    "updated_at": "datetime",
    "user": 0,
    "subcategory": 0,
    "category": 0|null
  }
]
```

## 5. 订单接口

### 5.1 创建订单
**请求URL**: `POST /api/orders/`  
**请求头**: `Authorization: Token <token>`  
**请求体**:
```json
{
  "service": 0, // 服务订单时必需
  "demand": 0,  // 需求订单时必需
  "order_type": "service|demand",
  "notes": "string|null"
}
```
**响应格式**:
```json
{
  "id": 0,
  "customer_name": "string",
  "provider_name": "string",
  "service_title": "string|null",
  "demand_title": "string|null",
  "order_no": "string",
  "order_type": "service|demand",
  "amount": "string|null",
  "provider_amount": "string|null",
  "customer_amount": "string|null",
  "status": "pending|confirmed|in_progress|completed_pending|completed|cancelled|refunded",
  "start_time": "datetime|null",
  "end_time": "datetime|null",
  "contact_info": {},
  "notes": "string|null",
  "created_at": "datetime",
  "updated_at": "datetime",
  "service": 0|null,
  "demand": 0|null,
  "customer": 0,
  "provider": 0|null
}
```

### 5.2 获取订单列表
**请求URL**: `GET /api/orders/`  
**查询参数**: 
- `status=<status>` (按状态筛选)
- `order_type=<order_type>` (按订单类型筛选)
- `customer=<customer_id>` (按客户筛选)
- `provider=<provider_id>` (按服务提供者筛选)
**响应格式**:
```json
[
  {
    "id": 0,
    "customer_name": "string",
    "provider_name": "string",
    "service_title": "string|null",
    "demand_title": "string|null",
    "order_no": "string",
    "order_type": "service|demand",
    "amount": "string|null",
    "provider_amount": "string|null",
    "customer_amount": "string|null",
    "status": "pending|confirmed|in_progress|completed_pending|completed|cancelled|refunded",
    "start_time": "datetime|null",
    "end_time": "datetime|null",
    "contact_info": {},
    "notes": "string|null",
    "created_at": "datetime",
    "updated_at": "datetime",
    "service": 0|null,
    "demand": 0|null,
    "customer": 0,
    "provider": 0|null
  }
]
```

### 5.3 更新订单
**请求URL**: `PUT /api/orders/{id}/`  
**请求头**: `Authorization: Token <token>`  
**请求体**:
```json
{
  "notes": "string|null",
  "contact_info": {}
}
```
**响应格式**:
```json
{
  "id": 0,
  "customer_name": "string",
  "provider_name": "string",
  "service_title": "string|null",
  "demand_title": "string|null",
  "order_no": "string",
  "order_type": "service|demand",
  "amount": "string|null",
  "provider_amount": "string|null",
  "customer_amount": "string|null",
  "status": "pending|confirmed|in_progress|completed_pending|completed|cancelled|refunded",
  "start_time": "datetime|null",
  "end_time": "datetime|null",
  "contact_info": {},
  "notes": "string|null",
  "created_at": "datetime",
  "updated_at": "datetime",
  "service": 0|null,
  "demand": 0|null,
  "customer": 0,
  "provider": 0|null
}
```

### 5.4 确认订单
**请求URL**: `PUT /api/orders/{id}/confirm_order/`  
**请求头**: `Authorization: Token <token>`  
**响应格式**:
```json
{
  "id": 0,
  "customer_name": "string",
  "provider_name": "string",
  "service_title": "string|null",
  "demand_title": "string|null",
  "order_no": "string",
  "order_type": "service|demand",
  "amount": "string|null",
  "provider_amount": "string|null",
  "customer_amount": "string|null",
  "status": "confirmed",
  "start_time": "datetime|null",
  "end_time": "datetime|null",
  "contact_info": {},
  "notes": "string|null",
  "created_at": "datetime",
  "updated_at": "datetime",
  "service": 0|null,
  "demand": 0|null,
  "customer": 0,
  "provider": 0|null
}
```

### 5.5 确认开始服务
**请求URL**: `PUT /api/orders/{id}/confirm_start/`  
**请求头**: `Authorization: Token <token>`  
**请求体**:
```json
{
  "start_time": "datetime"
}
```
**响应格式**:
```json
{
  "id": 0,
  "customer_name": "string",
  "provider_name": "string",
  "service_title": "string|null",
  "demand_title": "string|null",
  "order_no": "string",
  "order_type": "service|demand",
  "amount": "string|null",
  "provider_amount": "string|null",
  "customer_amount": "string|null",
  "status": "in_progress",
  "start_time": "datetime",
  "end_time": "datetime|null",
  "contact_info": {},
  "notes": "string|null",
  "created_at": "datetime",
  "updated_at": "datetime",
  "service": 0|null,
  "demand": 0|null,
  "customer": 0,
  "provider": 0|null
}
```

### 5.6 确认服务结束
**请求URL**: `PUT /api/orders/{id}/confirm_end/`  
**请求头**: `Authorization: Token <token>`  
**请求体**:
```json
{
  "amount": "number",
  "end_time": "datetime"
}
```
**响应格式**:
```json
{
  "id": 0,
  "customer_name": "string",
  "provider_name": "string",
  "service_title": "string|null",
  "demand_title": "string|null",
  "order_no": "string",
  "order_type": "service|demand",
  "amount": "string|null",
  "provider_amount": "string",
  "customer_amount": "string|null",
  "status": "completed_pending",
  "start_time": "datetime|null",
  "end_time": "datetime",
  "contact_info": {},
  "notes": "string|null",
  "created_at": "datetime",
  "updated_at": "datetime",
  "service": 0|null,
  "demand": 0|null,
  "customer": 0,
  "provider": 0|null
}
```

### 5.7 确认订单完成
**请求URL**: `PUT /api/orders/{id}/confirm_complete/`  
**请求头**: `Authorization: Token <token>`  
**请求体**:
```json
{
  "amount": "number"
}
```
**响应格式**:
```json
{
  "id": 0,
  "customer_name": "string",
  "provider_name": "string",
  "service_title": "string|null",
  "demand_title": "string|null",
  "order_no": "string",
  "order_type": "service|demand",
  "amount": "string|null",
  "provider_amount": "string",
  "customer_amount": "string",
  "status": "completed",
  "start_time": "datetime|null",
  "end_time": "datetime",
  "contact_info": {},
  "notes": "string|null",
  "created_at": "datetime",
  "updated_at": "datetime",
  "service": 0|null,
  "demand": 0|null,
  "customer": 0,
  "provider": 0|null
}
```

### 5.8 取消订单
**请求URL**: `PUT /api/orders/{id}/cancel/`  
**请求头**: `Authorization: Token <token>`  
**响应格式**:
```json
{
  "id": 0,
  "customer_name": "string",
  "provider_name": "string",
  "service_title": "string|null",
  "demand_title": "string|null",
  "order_no": "string",
  "order_type": "service|demand",
  "amount": "string|null",
  "provider_amount": "string|null",
  "customer_amount": "string|null",
  "status": "cancelled",
  "start_time": "datetime|null",
  "end_time": "datetime|null",
  "contact_info": {},
  "notes": "string|null",
  "created_at": "datetime",
  "updated_at": "datetime",
  "service": 0|null,
  "demand": 0|null,
  "customer": 0,
  "provider": 0|null
}
```

### 5.9 获取订单联系方式
**请求URL**: `GET /api/orders/{id}/contact/`  
**请求头**: `Authorization: Token <token>`  
**响应格式**:
```json
{}
```

## 6. 评价接口

### 6.1 发布评价
**请求URL**: `POST /api/reviews/`  
**请求头**: `Authorization: Token <token>`  
**请求体**:
```json
{
  "order": 0,
  "target_user": 0,
  "rating": 0, // 1-5的整数
  "content": "string|null",
  "images": ["string"], // 图片URL列表
  "is_anonymous": false
}
```
**响应格式**:
```json
{
  "id": 0,
  "reviewer_name": "string",
  "target_user_name": "string",
  "order_no": "string",
  "reply_user_name": "string|null",
  "rating": 0,
  "content": "string|null",
  "images": [],
  "is_anonymous": false,
  "created_at": "datetime",
  "updated_at": "datetime",
  "reply_content": "string|null",
  "replied_at": "datetime|null",
  "order": 0,
  "reviewer": 0,
  "target_user": 0,
  "reply_user": 0|null
}
```

### 6.2 获取评价列表
**请求URL**: `GET /api/reviews/`  
**查询参数**: 
- `order=<order_id>` (按订单筛选)
- `target_user=<user_id>` (按被评价用户筛选)
- `rating=<rating>` (按评分筛选)
**响应格式**:
```json
[
  {
    "id": 0,
    "reviewer_name": "string",
    "target_user_name": "string",
    "order_no": "string",
    "reply_user_name": "string|null",
    "rating": 0,
    "content": "string|null",
    "images": [],
    "is_anonymous": false,
    "created_at": "datetime",
    "updated_at": "datetime",
    "reply_content": "string|null",
    "replied_at": "datetime|null",
    "order": 0,
    "reviewer": 0,
    "target_user": 0,
    "reply_user": 0|null
  }
]
```

### 6.3 回复评价
**请求URL**: `PUT /api/reviews/{id}/reply/`  
**请求头**: `Authorization: Token <token>`  
**请求体**:
```json
{
  "reply_content": "string"
}
```
**响应格式**:
```json
{
  "id": 0,
  "reviewer_name": "string",
  "target_user_name": "string",
  "order_no": "string",
  "reply_user_name": "string",
  "rating": 0,
  "content": "string|null",
  "images": [],
  "is_anonymous": false,
  "created_at": "datetime",
  "updated_at": "datetime",
  "reply_content": "string",
  "replied_at": "datetime",
  "order": 0,
  "reviewer": 0,
  "target_user": 0,
  "reply_user": 0
}
```

## 7. 用户服务子类关联接口

### 7.1 申请服务子类认证
**请求URL**: `POST /api/user-service-subcategories/`  
**请求头**: `Authorization: Token <token>`  
**请求体**:
```json
{
  "subcategory": 0,
  "experience": "string|null",
  "certification": "string|null" // 认证图片URL
}
```
**响应格式**:
```json
{
  "id": 0,
  "user_name": "string",
  "subcategory_name": "string",
  "category_name": "string",
  "experience": "string|null",
  "certification": "string|null",
  "is_verified": false,
  "verified_at": "datetime|null",
  "created_at": "datetime",
  "updated_at": "datetime",
  "user": 0,
  "subcategory": 0,
  "verified_by": 0|null
}
```

### 7.2 获取认证列表
**请求URL**: `GET /api/user-service-subcategories/`  
**查询参数**: `is_verified=<true|false>` (按认证状态筛选)  
**响应格式**:
```json
[
  {
    "id": 0,
    "user_name": "string",
    "subcategory_name": "string",
    "category_name": "string",
    "experience": "string|null",
    "certification": "string|null",
    "is_verified": false,
    "verified_at": "datetime|null",
    "created_at": "datetime",
    "updated_at": "datetime",
    "user": 0,
    "subcategory": 0,
    "verified_by": 0|null
  }
]
```

### 7.3 审核认证申请
**请求URL**: `PUT /api/user-service-subcategories/{id}/verify/`  
**请求头**: `Authorization: Token <token>` (需管理员权限)  
**请求体**:
```json
{
  "is_verified": true
}
```
**响应格式**:
```json
{
  "id": 0,
  "user_name": "string",
  "subcategory_name": "string",
  "category_name": "string",
  "experience": "string|null",
  "certification": "string|null",
  "is_verified": true,
  "verified_at": "datetime",
  "created_at": "datetime",
  "updated_at": "datetime",
  "user": 0,
  "subcategory": 0,
  "verified_by": 0
}
```